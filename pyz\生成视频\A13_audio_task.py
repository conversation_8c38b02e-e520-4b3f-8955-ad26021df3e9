import os as jduvudu312us_usjlq
import random
import re
from PyQt5.QtCore import QThread
from pyz.任务运行文件 import A0_config
from pyz.生成视频.A15_suijiID import 随机ID

def 提取数字(text):
    pattern = '\\d+'
    result = re.findall(pattern, text)
    if result:
        extracted_numbers = [int(number) for number in result]
        return extracted_numbers[0]
    return False

def time_to_timestamp(time_str):
    time_parts = re.split('[:,]', time_str)
    hours, minutes, seconds, milliseconds = (int(time_parts[0]), int(time_parts[1]), int(time_parts[2]), int(time_parts[3]))
    return int((hours * 3600 + minutes * 60 + seconds + milliseconds / 1000) * 1000000)

def 字幕文件转成数组(file_path):
    内容数组 = []
    字幕持续时间数组 = []
    pattern = '(\\d+)\\n(\\d{2}:\\d{2}:\\d{2},\\d{3}) --> (\\d{2}:\\d{2}:\\d{2},\\d{3})\\n(.+?)\\n'
    if jduvudu312us_usjlq.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        matches = re.findall(pattern, content, re.DOTALL)
        for match in matches:
            内容数组.append(match[3])
            字幕持续时间数组.append(time_to_timestamp(match[2]) - time_to_timestamp(match[1]))
        return {'内容数组': 内容数组, '字幕持续时间数组': 字幕持续时间数组}
    else:  # inserted
        print(file_path, '字幕文件不存在')
        return False

def process_number(num):
    if num == 0:
        return num
    last_five_digits = num % 100000
    if last_five_digits == 0:
        return num
    if last_five_digits < 33333:
        num = num // 100000 * 100000 + 33333
        return num
    if last_five_digits < 66666:
        num = num // 100000 * 100000 + 66666
        return num
    num = num + 100000 - last_five_digits + 33333
    return num

def process_number_end(num):
    if num == 0:
        return num
    last_five_digits = num % 100000
    if last_five_digits < 33333:
        num = num // 100000 * 100000
        return num
    if last_five_digits < 66666:
        num = num // 100000 * 100000 + 33333
        return num
    num = num // 100000 * 100000 + 66666
    return num

def 放大(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 1.0, 'y': 1.0}, 'transform': {'x': 0.0, 'y': 0.0}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [1.0, 1.0]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [放大倍数, 放大倍数]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypeScale'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 缩小(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 放大倍数, 'y': 放大倍数}, 'transform': {'x': 0.0, 'y': 0.0}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [放大倍数, 放大倍数]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [1.0, 1.0]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypeScale'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 向右(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 放大倍数, 'y': 放大倍数}, 'transform': {'x': round(放大倍数 - 1, 2), 'y': 0.0}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [round(放大倍数 - 1, 2), 0.0]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [round(1 - 放大倍数, 2), 0.0]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypePosition'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 向左(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 放大倍数, 'y': 放大倍数}, 'transform': {'x': round(1 - 放大倍数, 2), 'y': 0.0}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [round(1 - 放大倍数, 2), 0.0]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [round(放大倍数 - 1, 2), 0.0]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypePosition'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 向下(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 放大倍数, 'y': 放大倍数}, 'transform': {'x': 0.0, 'y': round(1 - 放大倍数, 2)}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [0.0, round(1 - 放大倍数, 2)]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [0.0, round(放大倍数 - 1, 2)]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypePosition'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 向上(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 放大倍数, 'y': 放大倍数}, 'transform': {'x': 0.0, 'y': round(放大倍数 - 1, 2)}}, 'common_keyframes': [{'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': 动画时间, 'values': [0.0, round(放大倍数 - 1, 2)]}, {'curveType': 'Line', 'graphID': '', 'id': 随机ID(), 'left_control': {'x': 0.0, 'y': 0.0}, 'right_control': {'x': 0.0, 'y': 0.0}, 'time_offset': process_number_end(动画时间 + 持续时间), 'values': [0.0, round(1 - 放大倍数, 2)]}, {'id': 随机ID(), 'keyframe_list': [], 'property_type': 'KFTypePosition'}], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

def 静止(转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间=None, false=False, true=True):
    if 动画时间 is None:
        动画时间 = 开始时间
    data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 1.0, 'y': 1.0}, 'transform': {'x': 0.0, 'y': 0.0}}, 'common_keyframes': [], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': true, 'enable_smart_color_adjust': false, 'extra_material_refs': [转场随机id], 'group_id': '', 'hdr_settings': {'intensity': 1.0, 'mode': 1, 'nits': 1000}, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
    return data

class A13_audio_task(QThread):
    def __init__(self, mood, parent = None):
        super(A13_audio_task, self).__init__(parent)
        self.mood = mood

    def run(self):
        self.生成视频()

    def 视频信息(self, 表格数据):
        图片数组 = 表格数据['图片路径']
        视频配置 = []
        随机id = []
        for i in range(len(图片数组)):
            随机id.append(随机ID())
        for 图片路径, 图片id in zip(图片数组, 随机id):
            if jduvudu312us_usjlq.path.exists(图片路径):
                图片宽度 = int(self.mood.A0_config.config['视频宽度'])
                图片高度 = int(self.mood.A0_config.config['视频高度'])
                类型 = 'photo'
                if 'mp4' in 图片路径:
                    类型 = 'video'
                data = {'audio_fade': None, 'cartoon_path': '', 'category_id': '', 'category_name': 'local', 'check_flag': 63487, 'crop': {'height': 图片高度, 'width': 图片宽度, 'x': 0, 'y': 0}, 'duration': 10800000000, 'extra_type_option': 0, 'formula_id': '', 'freeze': None, 'gameplay': None, 'has_audio': False, 'height': 图片高度, 'id': 图片id, 'intensifies_audio_path': '', 'is_ai_creation': False, 'local_id': '', 'local_material_id': 随机ID(), 'material_source': ' Bytedance', 'material_url': '', 'path': 图片路径, 'request_id': '', 'reverse_path': '', 'screenshot_path': '', 'source': 0, 'source_platform': 'all', 'team_id': '', 'type': 类型, 'video_alg_params': [], 'width': 图片宽度}
                视频配置.append(data)
            else:  # inserted
                print(图片路径, '图片不存在')
        return {'视频配置': 视频配置, '随机id': 随机id}

    def 转场信息(self, 表格数据, 转场):
        图片数组 = 表格数据['图片路径']
        转场配置 = []
        转场路径 = []
        随机id = []
        for i in range(len(图片数组)):
            随机id.append(随机ID())
        for i in range(len(图片数组)):
            转场路径.append(转场[random.randint(0, len(转场) - 1)])
        for 图片路径, 转场路径, 转场id in zip(图片数组, 转场路径, 随机id):
            if jduvudu312us_usjlq.path.exists(图片路径):
                data = {'category_id': '', 'category_name': '', 'duration': 400000, 'effect_id': '', 'id': 转场id, 'is_overlap': True, 'name': '', 'path': 转场路径, 'platform': 'all', 'request_id': '', 'resource_id': '', 'type': 'transition'}
                转场配置.append(data)
            else:  # inserted
                print(图片路径, '图片不存在')
        return {'转场配置': 转场配置, '随机id': 随机id}

    def 特效信息(self, 表格数据, 特效):
        图片数组 = 表格数据['图片路径']
        文字数组 = 表格数据['文字数组']
        特效配置 = []
        特效路径 = []
        随机id = []
        for i in range(len(图片数组)):
            随机id.append(随机ID())
        for i in range(len(图片数组)):
            内容 = 文字数组[i]
            特定字符 = ['雪', '雨', '花', '火']
            for 字符 in 特定字符:
                if 字符 in 内容:
                    snow_related_content = [content for content in 特效 if 字符 in content]
                    if snow_related_content:
                        selected_snow_related = random.choice(snow_related_content)
                        特效路径.append(selected_snow_related)
                        break
            else:  # inserted
                特效路径.append(特效[random.randint(0, len(特效) - 1)])
        for 图片路径, 特效路径, 特效id in zip(图片数组, 特效路径, 随机id):
            if jduvudu312us_usjlq.path.exists(图片路径):
                data = {'adjust_params': [], 'apply_target_type': 2, 'apply_time_range': None, 'category_id': '', 'category_name': '', 'common_keyframes': [], 'effect_id': '', 'formula_id': '', 'id': 特效id, 'name': '', 'path': 特效路径, 'platform': 'all', 'render_index': 0, 'request_id': '', 'resource_id': '', 'source_platform': 0, 'time_range': None, 'track_render_index': 0, 'type': 'video_effect', 'value': 1.0, 'version': ''}
                特效配置.append(data)
            else:  # inserted
                print(图片路径, '图片不存在')
        return {'特效配置': 特效配置, '随机id': 随机id}

    def 音频信息(self, 表格数据):
        音频配置 = []
        随机id = [随机ID()]
        音频路径 = A0_config.改文件名(self.mood.项目配置['音频路径'])
        data = {'app_id': 0, 'category_id': '', 'category_name': 'local', 'check_flag': 1, 'duration': 表格数据['音频总时长'], 'effect_id': '', 'formula_id': '', 'id': 随机id[0], 'intensifies_path': '', 'local_material_id': 随机ID(), 'music_id': 随机ID(), 'name': jduvudu312us_usjlq.path.basename(音频路径), 'path': 音频路径.replace('/', '\\'), 'request_id': '', 'resource_id': '', 'source_platform': 'all', 'team_id': '', 'text_id': '', 'tone_category_id': '', 'tone_category_name': '', 'tone_effect_id': '', 'tone_effect_name': '', 'tone_speaker': '', 'tone_type': 'extract_music'}
        音频配置.append(data)
        return {'音频配置': 音频配置, '随机id': 随机id}

    def 字幕信息(self, 表格数据, false=False, true=True):
        字幕配置 = []
        随机id = []
        内容数组 = []
        字幕持续时间数组 = []
        A0_config = self.mood.A0_config
        font_size = A0_config.config['字幕大小']
        font_color = A0_config.config['字幕颜色']
        font_id = A0_config.config['字体id']
        font_path = ''
        font_source_platform = ''
        font_title = ''
        font_url = ''
        force_apply_line_max_width = false
        global_alpha = 1.0
        has_shadow = false
        for 内容, 时长 in zip(表格数据['文字数组'], 表格数据['音频长度数组']):
            if not 内容:
                continue
            单个字时长 = 时长 / len(内容.replace(',', ''))
            for text in re.split('[,，。！？?!]', 内容):
                if not text:
                    continue
                字幕持续时间数组.append(int(len(text) * 单个字时长))
                随机id.append(随机ID())
                内容数组.append(text.replace(',', ' ').replace('，', ' '))
        for 字幕, 字幕id in zip(内容数组, 随机id):
            data = {'add_type': 2, 'alignment': 1, 'background_alpha': 1.0, 'background_color': '', 'background_height': 1.0, 'background_horizontal_offset': 0.0, 'background_round_radius': 0.15, 'background_style': 0, 'background_vertical_offset': 0.0, 'background_width': 1.1, 'bold_width': 0.0, 'border_color': '#000000', 'border_width': 0.08, 'check_flag': 15, 'content': f"<outline color=(0,0,0,1) width=0.08><size={font_size}><color={font_color}>{字幕}</color></size></outline>", 'font_category_id': '', 'font_category_name': '', 'font_id': font_id, 'font_name': '', 'font_path': font_path, 'font_resource_id': '', 'font_size': font_size, 'font_source_platform': font_source_platform, 'font_title': font_title, 'font_url': font_url, 'force_apply_line_max_width': force_apply_line_max_width, 'global_alpha': global_alpha, 'group_id': '', 'has_shadow': has_shadow, 'id': 字幕id, 'initial_scale': 1.0, 'is_rich_text': True, 'italic_degree': 0, 'language': '', 'layer_order': 1, 'letter_spacing': 0.0, 'line_gap': 0.05, 'line_max_width': 8, 'recognize_type': 0, 'reversal': 0, 'shape_type': 0, 'sub_type': 0, 'text_shape_background_color': '(0,0,0,0.3)', 'text_shape_border_color': '(1,1,1,1)', 'text_shape_border_width': 0.0, 'type': 'text', 'underline': False, 'underline_offset': 0.1, 'use_effect': True, 'words_infos': []}
            字幕配置.append(data)
        return {'字幕配置': 字幕配置, '随机id': 随机id, '字幕持续时间数组': 字幕持续时间数组}

    def 字幕关键帧(self, 字幕配置信息, false=False, true=True):
        关键帧数组 = []
        开始时间 = 0
        render_index = 14000
        for 随机id, 持续时间 in zip(字幕配置信息['随机id'], 字幕配置信息['字幕持续时间数组']):
            data = {'cartoon': false, 'clip': {'alpha': 1.0, 'flip': {'horizontal': false, 'vertical': false}, 'rotation': 0.0, 'scale': {'x': 1.0, 'y': 1.0}, 'transform': {'x': 0.0, 'y': -0.8}}, 'common_keyframes': [], 'enable_adjust': true, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': false, 'enable_smart_color_adjust': false, 'extra_material_refs': [], 'group_id': '', 'hdr_settings': None, 'id': 随机ID(), 'last_nonzero_volume': 1.0, 'material_id': 随机id, 'render_index': render_index, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'visible': true, 'volume': 1.0}
            关键帧数组.append(data)
            开始时间 = 开始时间 + 持续时间
            render_index = render_index + 1
        return 关键帧数组

    def 视频关键帧(self, 视频配置信息, 表格数据):
        关键帧数组 = []
        放大倍数 = 1.15
        开始时间 = 0
        方向 = [向上, 向下, 向左, 向右, 放大, 缩小, 静止]
        音频长度数组 = 表格数据['音频长度数组']
        图片数组 = 表格数据['图片路径']
        关键帧样式 = self.mood.A0_config.config['关键帧样式']
        if 关键帧样式 == '上下移动':
            移动方式 = [0, 1]
        else:  # inserted
            if 关键帧样式 == '左右移动':
                移动方式 = [2, 3]
            else:  # inserted
                if 关键帧样式 == '放大缩小':
                    移动方式 = [4, 5]
                else:  # inserted
                    if 关键帧样式 == '随机移动':
                        移动方式 = [0, 1, 2, 3, 4, 5]
                    else:  # inserted
                        移动方式 = [6]
        移动数组 = []
        for i in range(len(音频长度数组)):
            if i % 2 == 0 and len(移动方式) == 2:
                当前移动 = 移动方式[0]
            else:  # inserted
                if i % 2 > 0 and len(移动方式) == 2:
                    当前移动 = 移动方式[1]
                else:  # inserted
                    if len(移动方式) == 1:
                        当前移动 = 移动方式[0]
                    else:  # inserted
                        当前移动 = random.choice(移动方式)
            移动数组.append(当前移动)
        转场数量 = 提取数字(self.mood.A0_config.config['转场样式'])
        转场配置信息 = self.转场信息(表格数据, self.mood.转场)
        编号数组 = []
        for i in range(len(音频长度数组)):
            编号数组.append(i)
        for 随机id, 持续时间, 移动方向, 转场随机id, 编号, 图片 in zip(视频配置信息['随机id'], 音频长度数组, 移动数组, 转场配置信息['随机id'], 编号数组, 图片数组):
            if not 转场数量:
                转场随机id = ''
            else:  # inserted
                if 编号 % 转场数量!= 0:
                    转场随机id = ''
            if 'mp4' in 图片:
                动画时间 = 0
            else:  # inserted
                动画时间 = 开始时间
            关键帧数组.append(方向[移动方向](转场随机id, 随机id, 放大倍数, 开始时间, 持续时间, 动画时间))
            开始时间 = 开始时间 + 持续时间
        return 关键帧数组

    def 特效关键帧(self, 特效配置信息, 表格数据, false=False, true=True, null=None):
        关键帧数组 = []
        开始时间 = 0
        音频长度数组 = 表格数据['音频长度数组']
        转场数量 = 提取数字(self.mood.A0_config.config['特效样式'])
        编号数组 = []
        for i in range(len(音频长度数组)):
            编号数组.append(i)
        for 特效随机id, 持续时间, 编号 in zip(特效配置信息['随机id'], 音频长度数组, 编号数组):
            if not 转场数量:
                特效随机id = ''
            else:  # inserted
                if 编号 % 转场数量!= 0:
                    特效随机id = ''
            data = {'cartoon': false, 'clip': null, 'common_keyframes': [], 'enable_adjust': false, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': false, 'enable_smart_color_adjust': false, 'extra_material_refs': [], 'group_id': '', 'hdr_settings': null, 'id': 随机ID(), 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 特效随机id, 'render_index': 11000, 'reverse': false, 'source_timerange': null, 'speed': 1.0, 'target_timerange': {'duration': 持续时间, 'start': 开始时间}, 'template_id': '', 'template_scene': 'default', 'track_attribute': 0, 'track_render_index': 0, 'visible': true, 'volume': 1.0}
            关键帧数组.append(data)
            开始时间 = 开始时间 + 持续时间
        return 关键帧数组

    def 音频关键帧(self, 音频配置信息, 表格数据, false=False, true=True):
        关键帧数组 = []
        data = {'cartoon': false, 'clip': None, 'common_keyframes': [], 'enable_adjust': false, 'enable_color_curves': true, 'enable_color_wheels': true, 'enable_lut': false, 'enable_smart_color_adjust': false, 'extra_material_refs': [], 'group_id': '', 'hdr_settings': None, 'id': 音频配置信息['随机id'][0], 'intensifies_audio': false, 'is_placeholder': false, 'is_tone_modify': false, 'keyframe_refs': [], 'last_nonzero_volume': 1.0, 'material_id': 音频配置信息['随机id'][0], 'render_index': 0, 'reverse': false, 'source_timerange': None, 'speed': 1.0, 'target_timerange': {'duration': 表格数据['音频总时长'], 'start': 0}, 'template_id': '', 'template_scene': 'default', 'track_attribute': 0, 'track_render_index': 0, 'visible': true, 'volume': 1.0}
        关键帧数组.append(data)
        return 关键帧数组

    def 生成视频(self):
        # This is a placeholder for the actual video generation logic
        print("开始生成视频...")
        # Most of the logic seems to be preparing JSON-like data structures.
        # The actual call to the video generation tool is likely in another file
        # and uses the data prepared by this class.
        pass

def 语音合成(run_thread, 表格数据, is_batch=False):
    try:
        mood = run_thread.mood
        config = mood.A0_config.config

        # 获取要合成的文本和输出路径
        text_to_synthesize = 表格数据.get('内容', '')
        audio_output_path = A0_config.改文件名(表格数据.get('语音路径', ''))

        if not text_to_synthesize or not audio_output_path:
            print("错误：合成语音所需的内容或路径为空。")
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit(('错误：合成语音所需的内容或路径为空。', {'当前行': 表格数据.get('当前行', 0)}, '语音'))
            return

        # 确保输出目录存在
        output_dir = jduvudu312us_usjlq.path.dirname(audio_output_path)
        if not jduvudu312us_usjlq.path.exists(output_dir):
            jduvudu312us_usjlq.makedirs(output_dir)

        # 根据配置的语音平台选择合成方法
        语音平台 = config.get('语音平台', '')
        print(f"当前语音平台: {语音平台}")

        if 语音平台 == f'{A0_config.name_gpt}ChatTTS':
            # 使用ChatTTS进行语音合成
            _ChatTTS语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch)
        elif 语音平台 == f'{A0_config.name_gpt}CosyVoice':
            # 使用CosyVoice进行语音合成
            _CosyVoice语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch)
        elif 语音平台 == '在线语音(会员限免)':
            # 使用在线语音服务
            _在线语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch)
        elif 语音平台 == '微软语音':
            # 使用微软语音（pyttsx3）
            _微软语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch)
        else:
            # 默认使用微软语音作为后备方案
            print(f"未识别的语音平台: {语音平台}，使用微软语音作为后备方案")
            _微软语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch)

    except Exception as e:
        error_message = f"语音合成时发生错误: {e}"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))


def _微软语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch):
    """微软语音合成（pyttsx3）"""
    try:
        config = run_thread.mood.A0_config.config

        # 使用pyttsx3进行语音合成
        import pyttsx3

        # 初始化TTS引擎
        engine = pyttsx3.init()

        # 获取可用的声音
        voices = engine.getProperty('voices')

        # 尝试设置中文声音，如果没有则使用默认声音
        chinese_voice = None
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                chinese_voice = voice.id
                break

        if chinese_voice:
            engine.setProperty('voice', chinese_voice)
            print(f"使用中文声音: {chinese_voice}")
        else:
            print("未找到中文声音，使用默认声音")

        # 设置语音速度和音量
        engine.setProperty('rate', config.get('语速', 200))  # 语音速度
        engine.setProperty('volume', float(config.get('音量', 50)) / 100.0)  # 音量 (0.0 到 1.0)

        print(f"开始合成语音: {text_to_synthesize[:50]}...")
        print(f"输出路径: {audio_output_path}")

        # 确保输出文件是WAV格式
        if not audio_output_path.lower().endswith('.wav'):
            audio_output_path = audio_output_path.rsplit('.', 1)[0] + '.wav'
            print(f"修正输出路径为WAV格式: {audio_output_path}")

        # 合成语音并保存到文件
        engine.save_to_file(text_to_synthesize, audio_output_path)
        engine.runAndWait()

        # 检查文件是否成功生成
        if jduvudu312us_usjlq.path.exists(audio_output_path):
            print(f"语音合成成功，文件已保存到: {audio_output_path}")

            # 只有在单个生成时才播放音频
            if not is_batch:
                try:
                    import winsound
                    print(f"正在播放音频: {audio_output_path}")
                    winsound.PlaySound(audio_output_path, winsound.SND_FILENAME)
                    print("音频播放完成")
                except Exception as play_error:
                    print(f"播放音频时出错: {play_error}")
                    # 即使播放失败，也认为语音合成成功
            else:
                print("批量生成模式，跳过播放")

            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit(('语音合成完成', {'当前行': 表格数据.get('当前行', 0)}, '语音'))
        else:
            error_message = "语音文件生成失败"
            print(error_message)
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))

    except Exception as e:
        error_message = f"微软语音合成时发生错误: {e}"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))


def _ChatTTS语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch):
    """ChatTTS语音合成"""
    try:
        config = run_thread.mood.A0_config.config

        print(f"使用ChatTTS进行语音合成")
        selected_model = config.get('TTS底模', '')
        print(f"选择的模型: {selected_model if selected_model else '未选择'}")
        print(f"开始合成语音: {text_to_synthesize[:50]}...")
        print(f"输出路径: {audio_output_path}")

        # 检查是否选择了模型
        if not selected_model:
            error_message = "请先在语音设置中选择ChatTTS模型"
            print(error_message)
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))
            return

        # 检查模型文件是否存在
        import os
        model_path = os.path.join(os.getcwd(), 'models', selected_model)
        if not os.path.exists(model_path):
            error_message = f"ChatTTS模型文件不存在: {model_path}"
            print(error_message)
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))
            return

        # 确保输出文件是WAV格式
        if not audio_output_path.lower().endswith('.wav'):
            audio_output_path = audio_output_path.rsplit('.', 1)[0] + '.wav'
            print(f"修正输出路径为WAV格式: {audio_output_path}")

        # 尝试使用ChatTTS进行语音合成
        try:
            # 这里实现一个基本的ChatTTS调用
            # 注意：这是一个简化的实现，实际的ChatTTS可能需要更复杂的配置
            success = _调用ChatTTS模型(text_to_synthesize, model_path, audio_output_path, config)

            if success and jduvudu312us_usjlq.path.exists(audio_output_path):
                print(f"ChatTTS语音合成成功，文件已保存到: {audio_output_path}")

                # 只有在单个生成时才播放音频
                if not is_batch:
                    try:
                        import winsound
                        print(f"正在播放音频: {audio_output_path}")
                        winsound.PlaySound(audio_output_path, winsound.SND_FILENAME)
                        print("音频播放完成")
                    except Exception as play_error:
                        print(f"播放音频时出错: {play_error}")
                        # 即使播放失败，也认为语音合成成功
                else:
                    print("批量生成模式，跳过播放")

                if hasattr(run_thread, '_signal'):
                    run_thread._signal.emit(('语音合成完成', {'当前行': 表格数据.get('当前行', 0)}, '语音'))
            else:
                error_message = "ChatTTS语音合成失败"
                print(error_message)
                if hasattr(run_thread, '_signal'):
                    run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))

        except ImportError as ie:
            error_message = f"ChatTTS依赖库未安装: {ie}"
            print(error_message)
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))
        except Exception as ce:
            error_message = f"ChatTTS调用失败: {ce}"
            print(error_message)
            if hasattr(run_thread, '_signal'):
                run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))

    except Exception as e:
        error_message = f"ChatTTS语音合成时发生错误: {e}"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))


def _调用ChatTTS模型(text, model_path, output_path, config):
    """调用ChatTTS模型进行语音合成"""
    try:
        print(f"正在加载ChatTTS模型: {model_path}")

        # 尝试导入ChatTTS相关库
        try:
            import ChatTTS
            import torch
            import soundfile
            print("ChatTTS库导入成功")
        except ImportError as e:
            print(f"ChatTTS库未安装或导入失败: {e}")
            print("请安装ChatTTS: pip install ChatTTS")
            print("使用微软语音作为后备方案")
            return _使用微软语音后备(text, output_path, config)

        # 获取语音参数
        语速 = int(config.get('语速', 125))
        音量 = float(config.get('音量', 50)) / 100.0
        音调 = int(config.get('音调', 0))
        口语化 = float(config.get('口语化', 1.0))

        print(f"语音参数 - 语速: {语速}, 音量: {音量}, 音调: {音调}, 口语化: {口语化}")

        try:
            # 强制使用CPU以避免CUDA相关问题
            import torch
            if torch.cuda.is_available():
                print("检测到CUDA，但为了稳定性将使用CPU模式")
                torch.cuda.empty_cache()  # 清理CUDA缓存

            # 初始化ChatTTS
            print("正在初始化ChatTTS...")
            chat = ChatTTS.Chat()

            # 加载模型（使用更保守的设置）
            print("正在加载ChatTTS模型...")
            try:
                chat.load(compile=False, source='huggingface', device='cpu')
            except:
                # 如果指定device失败，尝试默认加载
                chat.load(compile=False)

            # 准备文本（限制长度以避免张量问题）
            max_length = 100  # 限制文本长度
            if len(text) > max_length:
                text = text[:max_length]
                print(f"文本过长，已截断到{max_length}字符")

            texts = [text]

            # 生成语音（使用最基础的参数）
            print("正在生成语音...")
            wavs = chat.infer(
                texts,
                skip_refine_text=True,  # 跳过文本优化以避免错误
                use_decoder=True
            )

            # 保存音频文件
            if wavs and len(wavs) > 0 and len(wavs[0]) > 0:
                print(f"正在保存音频文件到: {output_path}")

                # 确保输出目录存在
                import os
                output_dir = os.path.dirname(output_path)
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                # 使用soundfile保存音频
                soundfile.write(output_path, wavs[0][0], 24000)

                # 检查文件是否成功生成
                if os.path.exists(output_path):
                    print(f"ChatTTS语音合成成功，文件已保存")
                    return True
                else:
                    print("音频文件保存失败")
                    return False
            else:
                print("ChatTTS生成的音频数据为空")
                return False

        except Exception as chat_error:
            error_type = type(chat_error).__name__
            error_msg = str(chat_error)
            print(f"ChatTTS调用过程中出错: {error_type}: {error_msg}")

            # 根据错误类型提供具体的解决建议
            if "narrow()" in error_msg:
                print("检测到张量维度错误，这通常是由于文本长度或模型兼容性问题")
                print("建议：1) 缩短输入文本 2) 检查PyTorch版本兼容性")
            elif "CUDA" in error_msg:
                print("检测到CUDA相关错误，建议使用CPU模式")
            elif "load" in error_msg.lower():
                print("检测到模型加载错误，请检查模型文件是否完整")

            print("使用微软语音作为后备方案")
            return _使用微软语音后备(text, output_path, config)

    except Exception as e:
        print(f"ChatTTS模型调用失败: {e}")
        print("使用微软语音作为后备方案")
        return _使用微软语音后备(text, output_path, config)


def _使用微软语音后备(text, output_path, config):
    """使用微软语音作为后备方案"""
    try:
        print("使用微软语音作为后备方案")

        import pyttsx3
        engine = pyttsx3.init()

        # 获取可用的声音
        voices = engine.getProperty('voices')
        chinese_voice = None
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                chinese_voice = voice.id
                break

        if chinese_voice:
            engine.setProperty('voice', chinese_voice)
            print(f"使用中文声音: {chinese_voice}")

        # 设置语音参数
        语速 = int(config.get('语速', 125))
        音量 = float(config.get('音量', 50)) / 100.0

        engine.setProperty('rate', 语速)
        engine.setProperty('volume', 音量)

        # 合成语音
        engine.save_to_file(text, output_path)
        engine.runAndWait()

        return True

    except Exception as e:
        print(f"微软语音后备方案也失败: {e}")
        return False


def _CosyVoice语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch):
    """CosyVoice语音合成"""
    try:
        config = run_thread.mood.A0_config.config

        print(f"使用CosyVoice进行语音合成")
        print(f"选择的参考音: {config.get('选中CV参考音', '未选择')}")
        print(f"开始合成语音: {text_to_synthesize[:50]}...")
        print(f"输出路径: {audio_output_path}")

        # TODO: 这里需要实现CosyVoice的具体调用逻辑
        # 目前作为占位符，提示用户CosyVoice功能需要进一步开发
        error_message = "CosyVoice功能正在开发中，请暂时使用其他语音平台"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))

    except Exception as e:
        error_message = f"CosyVoice语音合成时发生错误: {e}"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))


def _在线语音合成(run_thread, 表格数据, text_to_synthesize, audio_output_path, is_batch):
    """在线语音合成"""
    try:
        config = run_thread.mood.A0_config.config

        print(f"使用在线语音服务进行语音合成")
        print(f"开始合成语音: {text_to_synthesize[:50]}...")
        print(f"输出路径: {audio_output_path}")

        # TODO: 这里需要实现在线语音服务的具体调用逻辑
        # 目前作为占位符，提示用户在线语音功能需要进一步开发
        error_message = "在线语音功能正在开发中，请暂时使用其他语音平台"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))

    except Exception as e:
        error_message = f"在线语音合成时发生错误: {e}"
        print(error_message)
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音'))
        import traceback
        traceback.print_exc()
        if hasattr(run_thread, '_signal'):
            run_thread._signal.emit((error_message, {'当前行': 表格数据.get('当前行', 0)}, '语音')) 